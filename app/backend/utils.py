import docx
from docx.opc.constants import RELATIONSHIP_TYPE as RT
from PyPDF2 import Pdf<PERSON><PERSON>er
import re
import subprocess
import json
from config.prompts import PromptConfig
# from spire.pdf import *
# from spire.pdf.common import *
 



# file contains all llm prompts and the functionality to parse and read uploaded documents

# COMMENTED OUT: LinkedIn functionality disabled for new interviews
# def get_linkedin_prompt(resume_text, linkedin_data):
#     prompt = f'''
#     Here is a candidates resume:
#
#     {resume_text}
#
#     And here is the candidates linkedin profile data:
#
#     {linkedin_data}
#
#     Compare their linkedin profile with their resume. Note any key differences, especially skills and experience listed on their resume that is not on their linkedin profile. Also note if they do not have a profile picture on their linkedin account since this is typically a sign of a bad candidate.
#     '''
#     return prompt


def get_main_prompts(resume_text, job_text, linkedInID):
    """
    Get all main prompts formatted with the provided data.
    Uses the centralized PromptConfig for consistency.
    """
    return PromptConfig.get_all_main_prompts(resume_text, job_text, linkedInID)

def regenerate_technical(job_text, report, number):
    """
    Generate a technical question regeneration prompt.
    Uses the centralized PromptConfig for consistency.
    """
    template = PromptConfig.get_regeneration_prompt("technical_regeneration")
    return template.format(job_text=job_text, report=report, number=number)

def regenerate_behavioral(job_text, report, number):
    """
    Generate a behavioral question regeneration prompt.
    Uses the centralized PromptConfig for consistency.
    """
    template = PromptConfig.get_regeneration_prompt("behavioral_regeneration")
    return template.format(job_text=job_text, report=report, number=number)

supported_file_types = {
    "application/pdf": ".pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ".docx",
    "text/plain": ".txt",
    "application/msword": ".doc"
}

supported_file_types_list = [val for val in supported_file_types.values()]

def check_file_extension(uploaded_file):
    """
    Check the file extension of the uploaded file and return its content.

    Args:
        uploaded_file: The uploaded file object.

    Returns:
        The content of the uploaded file.

    Raises:
        Exception: If file processing fails.
    """
    try:
        file_content = ""
        if isinstance(uploaded_file, str):
            file_content = uploaded_file
        else:
            if not hasattr(uploaded_file, 'content_type'):
                raise Exception("Invalid file object - missing content_type")

            if uploaded_file.content_type == "application/pdf":
                file_content = parse_pdf(uploaded_file)
            elif uploaded_file.content_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                file_content = parse_docx(uploaded_file)
            elif uploaded_file.content_type == "text/plain":
                file_content = uploaded_file.read().decode("utf-8")
            elif uploaded_file.content_type == "application/msword":
                file_content = parse_doc(uploaded_file)
            else:
                raise Exception(f"Unsupported file type: {uploaded_file.content_type}")

        if not file_content or len(file_content.strip()) == 0:
            raise Exception("File appears to be empty or contains no readable text")

        return file_content
    except Exception as e:
        print(f"Error in check_file_extension: {str(e)}")
        raise

def parse_pdf(file):
    try:
        pdf = PdfReader(file)
        output = []
        for page in pdf.pages:
          #  extract_images(page=page)
            text = page.extract_text()
            # Merge hyphenated words
            text = re.sub(r"(\w+)-\n(\w+)", r"\1\2", text)
            # Fix newlines in the middle of sentences
            text = re.sub(r"(?<!\n\s)\n(?!\s\n)", " ", text.strip())
            # Remove multiple newlines
            text = re.sub(r"\n\s*\n", "\n\n", text)

            output.append(text)

        # Join all pages into a single string
        return "\n\n".join(output)
    except Exception as e:
        print(f"Error parsing PDF: {str(e)}")
        raise Exception(f"Failed to parse PDF file: {str(e)}")

def parse_docx(file):
    try:
        doc = docx.Document(file)
        rels = doc.part.rels

        # Create a list to store the extracted hyperlinks
        extracted_hyperlinks = []
        for hyperlink in iter_hyperlink_rels(rels):
            extracted_hyperlinks.append(hyperlink)

        # Join the hyperlinks into a single string
        hyperlinks_string = '\n'.join(extracted_hyperlinks)

        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + '\n'

        return text + hyperlinks_string
    except Exception as e:
        print(f"Error parsing DOCX: {str(e)}")
        raise Exception(f"Failed to parse DOCX file: {str(e)}")

def iter_hyperlink_rels(rels):
    """
    Iterate over the hyperlink relationships in the given dictionary of relationships.

    Args:
        rels (dict): A dictionary of relationships.

    Yields:
        str: The target of each hyperlink relationship.

    """
    for rel in rels:
        if rels[rel].reltype == RT.HYPERLINK:
            yield rels[rel]._target

import subprocess

def parse_doc(doc_file):
    """
    Parses a DOC file using the 'antiword' command-line tool.

    Args:
        doc_file (file-like object): The DOC file to be parsed.

    Returns:
        str: The parsed content of the DOC file as a string, or None if an error occurs.
    """
    try:
        doc_file_data = doc_file.read()

        process = subprocess.Popen(
            ['antiword', '-'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = process.communicate(input=doc_file_data)
        if stderr:
            print(f"Error: {stderr}")
            
        return stdout

    except subprocess.CalledProcessError:
        return None
    

# def extract_PDFimages(file):
#     pdf = PdfDocument()
#
#     pdf.LoadFromFile(file)
#
#     images = []
#
#     for i in range(pdf.Pages.Count):
#         page = pdf.Pages.get_Item(i)
#         for img in page.ExtractImages():
#             images.append(img)
#
#     i = 0
#     for image in images:
#         i += 1
#         image.Save("/tmp/Image-{0:d}.png".format(i), ImageFormat.get_Png())
#
#     pdf.Close()
#     return images

def extract_PDFimages(file):
    """Placeholder function - Spire.Pdf not available"""
    print("Warning: PDF image extraction not available (Spire.Pdf not installed)")
    return []

def extract_text_from_file(uploaded_file):
    """
    Extract text from uploaded file.
    This is an alias for check_file_extension for compatibility.

    Args:
        uploaded_file: The uploaded file object.

    Returns:
        str: The extracted text content.
    """
    return check_file_extension(uploaded_file)

def is_valid_json(json_string):
    """
    Check if a string is valid JSON.

    Args:
        json_string (str): The string to validate.

    Returns:
        bool: True if valid JSON, False otherwise.
    """
    try:
        json.loads(json_string)
        return True
    except (json.JSONDecodeError, TypeError):
        return False

